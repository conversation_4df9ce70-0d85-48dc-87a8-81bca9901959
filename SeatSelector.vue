<template>
  <div class="seat-selector">
    <!-- 标题和说明 -->
    <div class="header">
      <h2>选择座位</h2>
      <div class="legend">
        <div class="legend-item">
          <div class="seat-demo available"></div>
          <span>可选</span>
        </div>
        <div class="legend-item">
          <div class="seat-demo selected"></div>
          <span>已选</span>
        </div>
        <div class="legend-item">
          <div class="seat-demo occupied"></div>
          <span>不可选</span>
        </div>
      </div>
    </div>

    <!-- 屏幕 -->
    <div class="screen-area">
      <div class="screen">
        <div class="screen-text">屏幕</div>
      </div>
    </div>

    <!-- 座位区域 -->
    <div class="seats-container">
      <div 
        v-for="(row, rowIndex) in seatLayout" 
        :key="rowIndex"
        class="seat-row"
        :style="getRowStyle(rowIndex)"
      >
        <!-- 行号 -->
        <div class="row-label">{{ String.fromCharCode(65 + rowIndex) }}</div>
        
        <!-- 左侧座位区域 -->
        <div class="seat-section left-section">
          <div
            v-for="(seat, seatIndex) in row.leftSection"
            :key="`left-${seatIndex}`"
            class="seat"
            :class="[
              seat.status,
              { 'clickable': seat.status === 'available' || seat.status === 'selected' }
            ]"
            @click="handleSeatClick(rowIndex, 'left', seatIndex)"
          >
            <div class="seat-number">{{ seatIndex + 1 }}</div>
          </div>
        </div>

        <!-- 中间过道 -->
        <div class="aisle center-aisle">
          <div class="aisle-marker"></div>
        </div>

        <!-- 中间座位区域 -->
        <div class="seat-section center-section">
          <div
            v-for="(seat, seatIndex) in row.centerSection"
            :key="`center-${seatIndex}`"
            class="seat"
            :class="[
              seat.status,
              { 'clickable': seat.status === 'available' || seat.status === 'selected' }
            ]"
            @click="handleSeatClick(rowIndex, 'center', seatIndex)"
          >
            <div class="seat-number">{{ row.leftSection.length + seatIndex + 1 }}</div>
          </div>
        </div>

        <!-- 右侧过道 -->
        <div class="aisle right-aisle">
          <div class="aisle-marker"></div>
        </div>

        <!-- 右侧座位区域 -->
        <div class="seat-section right-section">
          <div
            v-for="(seat, seatIndex) in row.rightSection"
            :key="`right-${seatIndex}`"
            class="seat"
            :class="[
              seat.status,
              { 'clickable': seat.status === 'available' || seat.status === 'selected' }
            ]"
            @click="handleSeatClick(rowIndex, 'right', seatIndex)"
          >
            <div class="seat-number">{{ row.leftSection.length + row.centerSection.length + seatIndex + 1 }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 选中的座位信息 -->
    <div class="selected-info" v-if="selectedSeats.length > 0">
      <h3>已选座位:</h3>
      <div class="selected-seats">
        <span 
          v-for="seat in selectedSeats" 
          :key="`${seat.row}-${seat.section}-${seat.seat}`"
          class="selected-seat-tag"
        >
          {{ getSeatDisplayNumber(seat) }}
          <button @click="removeSeat(seat.row, seat.section, seat.seat)" class="remove-btn">×</button>
        </span>
      </div>
      <div class="total-price">
        总价: ¥{{ totalPrice }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SeatSelector',
  data() {
    return {
      // 座位布局数据 - 分区域的电影院座位
      seatLayout: [
        // 前排 - 更靠近屏幕，弧度更大
        {
          leftSection: [
            { status: 'available', price: 35 },
            { status: 'available', price: 35 },
            { status: 'occupied', price: 35 }
          ],
          centerSection: [
            { status: 'available', price: 35 },
            { status: 'available', price: 35 },
            { status: 'available', price: 35 },
            { status: 'available', price: 35 }
          ],
          rightSection: [
            { status: 'occupied', price: 35 },
            { status: 'available', price: 35 },
            { status: 'available', price: 35 }
          ]
        },
        {
          leftSection: [
            { status: 'available', price: 40 },
            { status: 'available', price: 40 },
            { status: 'available', price: 40 }
          ],
          centerSection: [
            { status: 'occupied', price: 40 },
            { status: 'occupied', price: 40 },
            { status: 'available', price: 40 },
            { status: 'available', price: 40 }
          ],
          rightSection: [
            { status: 'available', price: 40 },
            { status: 'available', price: 40 },
            { status: 'available', price: 40 }
          ]
        },
        {
          leftSection: [
            { status: 'available', price: 45 },
            { status: 'available', price: 45 },
            { status: 'available', price: 45 }
          ],
          centerSection: [
            { status: 'available', price: 45 },
            { status: 'available', price: 45 },
            { status: 'available', price: 45 },
            { status: 'available', price: 45 }
          ],
          rightSection: [
            { status: 'available', price: 45 },
            { status: 'available', price: 45 },
            { status: 'available', price: 45 }
          ]
        },
        // 中间排 - 黄金座位区
        {
          leftSection: [
            { status: 'available', price: 50 },
            { status: 'available', price: 50 },
            { status: 'available', price: 50 }
          ],
          centerSection: [
            { status: 'available', price: 50 },
            { status: 'available', price: 50 },
            { status: 'available', price: 50 },
            { status: 'available', price: 50 }
          ],
          rightSection: [
            { status: 'available', price: 50 },
            { status: 'available', price: 50 },
            { status: 'available', price: 50 }
          ]
        },
        {
          leftSection: [
            { status: 'available', price: 50 },
            { status: 'available', price: 50 },
            { status: 'available', price: 50 }
          ],
          centerSection: [
            { status: 'occupied', price: 50 },
            { status: 'occupied', price: 50 },
            { status: 'occupied', price: 50 },
            { status: 'occupied', price: 50 }
          ],
          rightSection: [
            { status: 'available', price: 50 },
            { status: 'available', price: 50 },
            { status: 'available', price: 50 }
          ]
        },
        {
          leftSection: [
            { status: 'available', price: 50 },
            { status: 'available', price: 50 },
            { status: 'available', price: 50 }
          ],
          centerSection: [
            { status: 'available', price: 50 },
            { status: 'available', price: 50 },
            { status: 'available', price: 50 },
            { status: 'available', price: 50 }
          ],
          rightSection: [
            { status: 'available', price: 50 },
            { status: 'available', price: 50 },
            { status: 'available', price: 50 }
          ]
        },
        // 后排 - 弧度较小
        {
          leftSection: [
            { status: 'available', price: 45 },
            { status: 'available', price: 45 },
            { status: 'available', price: 45 }
          ],
          centerSection: [
            { status: 'available', price: 45 },
            { status: 'occupied', price: 45 },
            { status: 'available', price: 45 },
            { status: 'available', price: 45 }
          ],
          rightSection: [
            { status: 'available', price: 45 },
            { status: 'available', price: 45 },
            { status: 'available', price: 45 }
          ]
        },
        {
          leftSection: [
            { status: 'available', price: 40 },
            { status: 'available', price: 40 },
            { status: 'available', price: 40 }
          ],
          centerSection: [
            { status: 'available', price: 40 },
            { status: 'available', price: 40 },
            { status: 'available', price: 40 },
            { status: 'available', price: 40 }
          ],
          rightSection: [
            { status: 'available', price: 40 },
            { status: 'available', price: 40 },
            { status: 'available', price: 40 }
          ]
        }
      ],
      selectedSeats: []
    }
  },
  computed: {
    totalPrice() {
      return this.selectedSeats.reduce((total, seat) => {
        const seatData = this.seatLayout[seat.row][seat.section][seat.seat];
        return total + seatData.price;
      }, 0);
    }
  },
  methods: {
    // 计算每行的样式，包括弧度和角度
    getRowStyle(rowIndex) {
      const totalRows = this.seatLayout.length;
      const centerRow = (totalRows - 1) / 2;
      
      // 计算距离中心行的距离
      const distanceFromCenter = rowIndex - centerRow;
      
      // 弧度半径 - 后排半径更大，前排半径更小（更弯曲）
      const baseRadius = 300;
      const radiusVariation = 100;
      const radius = baseRadius + (distanceFromCenter * radiusVariation);
      
      // 计算角度 - 前排座位更倾向于屏幕
      const maxAngle = 15; // 最大角度（度）
      const angle = -(distanceFromCenter / totalRows) * maxAngle;
      
      // 计算Y轴偏移 - 形成弧形
      const yOffset = Math.abs(distanceFromCenter) * 8;
      
      return {
        transform: `translateY(${yOffset}px) rotate(${angle}deg)`,
        transformOrigin: `center ${radius}px`
      };
    },
    
    // 处理座位点击
    handleSeatClick(rowIndex, section, seatIndex) {
      const seat = this.seatLayout[rowIndex][section][seatIndex];
      
      if (seat.status === 'occupied') {
        return; // 不可选的座位不能点击
      }
      
      if (seat.status === 'available') {
        // 选中座位
        seat.status = 'selected';
        this.selectedSeats.push({ 
          row: rowIndex, 
          section: section, 
          seat: seatIndex 
        });
      } else if (seat.status === 'selected') {
        // 取消选中
        seat.status = 'available';
        this.selectedSeats = this.selectedSeats.filter(
          s => !(s.row === rowIndex && s.section === section && s.seat === seatIndex)
        );
      }
    },
    
    // 移除选中的座位
    removeSeat(rowIndex, section, seatIndex) {
      this.seatLayout[rowIndex][section][seatIndex].status = 'available';
      this.selectedSeats = this.selectedSeats.filter(
        s => !(s.row === rowIndex && s.section === section && s.seat === seatIndex)
      );
    },
    
    // 获取座位的显示编号
    getSeatDisplayNumber(seat) {
      const row = String.fromCharCode(65 + seat.row);
      let seatNumber;
      
      if (seat.section === 'left') {
        seatNumber = seat.seat + 1;
      } else if (seat.section === 'center') {
        const leftCount = this.seatLayout[seat.row].leftSection.length;
        seatNumber = leftCount + seat.seat + 1;
      } else { // right
        const leftCount = this.seatLayout[seat.row].leftSection.length;
        const centerCount = this.seatLayout[seat.row].centerSection.length;
        seatNumber = leftCount + centerCount + seat.seat + 1;
      }
      
      return `${row}${seatNumber}`;
    }
  }
}
</script>

<style scoped>
.seat-selector {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Arial', sans-serif;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  min-height: 100vh;
  color: white;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h2 {
  margin: 0 0 20px 0;
  font-size: 28px;
  font-weight: bold;
}

.legend {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-bottom: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.seat-demo {
  width: 20px;
  height: 20px;
  border-radius: 8px 8px 2px 2px;
  border: 2px solid #333;
}

.screen-area {
  text-align: center;
  margin-bottom: 40px;
}

.screen {
  background: linear-gradient(to bottom, #444 0%, #222 100%);
  border: 3px solid #666;
  border-radius: 50px;
  padding: 15px 60px;
  margin: 0 auto;
  max-width: 400px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
  transform: perspective(300px) rotateX(15deg);
}

.screen-text {
  font-size: 18px;
  font-weight: bold;
  color: #ccc;
  letter-spacing: 2px;
}

.seats-container {
  margin: 40px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  perspective: 1000px;
}

.seat-row {
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all 0.3s ease;
  min-width: 600px;
  justify-content: center;
}

.row-label {
  font-weight: bold;
  font-size: 16px;
  width: 30px;
  text-align: center;
  color: #bbb;
  position: absolute;
  left: -50px;
}

.seat-section {
  display: flex;
  gap: 8px;
}

.left-section {
  margin-right: 5px;
}

.center-section {
  margin: 0 5px;
}

.right-section {
  margin-left: 5px;
}

.aisle {
  width: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.aisle-marker {
  width: 2px;
  height: 25px;
  background: linear-gradient(to bottom, 
    transparent 0%, 
    rgba(255, 255, 255, 0.3) 20%, 
    rgba(255, 255, 255, 0.6) 50%, 
    rgba(255, 255, 255, 0.3) 80%, 
    transparent 100%
  );
  border-radius: 1px;
}

.center-aisle .aisle-marker {
  background: linear-gradient(to bottom, 
    transparent 0%, 
    rgba(255, 255, 255, 0.4) 20%, 
    rgba(255, 255, 255, 0.8) 50%, 
    rgba(255, 255, 255, 0.4) 80%, 
    transparent 100%
  );
}

.seat {
  width: 35px;
  height: 35px;
  border-radius: 8px 8px 2px 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  border: 2px solid;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.seat::before {
  content: '';
  position: absolute;
  bottom: -3px;
  left: 50%;
  transform: translateX(-50%);
  width: 25px;
  height: 3px;
  background: currentColor;
  border-radius: 0 0 3px 3px;
}

.seat.available {
  background: #4CAF50;
  border-color: #45a049;
  color: white;
}

.seat.available:hover {
  background: #66BB6A;
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

.seat.selected {
  background: #FF9800;
  border-color: #F57C00;
  color: white;
  animation: pulse 2s infinite;
}

.seat.occupied {
  background: #f44336;
  border-color: #d32f2f;
  color: white;
  cursor: not-allowed;
  opacity: 0.7;
}

.seat.clickable {
  cursor: pointer;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.seat-number {
  font-size: 11px;
}

.selected-info {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 20px;
  margin-top: 30px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.selected-info h3 {
  margin: 0 0 15px 0;
  font-size: 18px;
}

.selected-seats {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 15px;
}

.selected-seat-tag {
  background: #FF9800;
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 8px;
}

.remove-btn {
  background: rgba(255, 255, 255, 0.3);
  border: none;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 16px;
  transition: background 0.3s ease;
}

.remove-btn:hover {
  background: rgba(255, 255, 255, 0.5);
}

.total-price {
  font-size: 20px;
  font-weight: bold;
  text-align: right;
  color: #4CAF50;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: 15px;
}

/* 添加座位区域的弧度动画效果 */
.seat-section {
  transform-style: preserve-3d;
}

.left-section {
  transform: rotateY(8deg);
  transform-origin: right center;
}

.center-section {
  transform: rotateY(0deg);
}

.right-section {
  transform: rotateY(-8deg);
  transform-origin: left center;
}

/* 座位行的3D透视效果 */
.seat-row {
  position: relative;
  transform-style: preserve-3d;
}

/* 过道装饰效果 */
.aisle::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .seat-selector {
    padding: 15px;
  }
  
  .legend {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }
  
  .seat {
    width: 30px;
    height: 30px;
    font-size: 10px;
  }
  
  .seat-section {
    gap: 6px;
  }
  
  .seat-row {
    gap: 10px;
    min-width: 400px;
  }
  
  .aisle {
    width: 20px;
  }
  
  .row-label {
    left: -40px;
    font-size: 14px;
  }
  
  /* 移动端减少3D效果 */
  .left-section, .right-section {
    transform: none;
  }
}
</style>