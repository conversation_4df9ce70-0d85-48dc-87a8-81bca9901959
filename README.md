# Vue选座组件

一个功能完整的Vue 3选座组件，适用于电影院、剧院等场景。

## 功能特性

- ✅ 三种座位状态：可选、已选、不可选
- ✅ 弧形屏幕设计和座椅弧度布局
- ✅ 实时价格计算
- ✅ 响应式设计，支持移动端
- ✅ 平滑的动画效果
- ✅ 直观的用户界面

## 快速开始

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

## 组件说明

### SeatSelector组件

主要的选座组件，包含以下功能：

1. **座位状态管理**
   - `available`：可选择的座位（绿色）
   - `selected`：已选择的座位（橙色，带脉冲动画）
   - `occupied`：已占用的座位（红色，不可点击）

2. **弧形布局**
   - 根据行数自动计算座位偏移量
   - 模拟真实电影院的弧形座位布局
   - 前排座位有更大的弧度

3. **交互功能**
   - 点击座位进行选择/取消
   - 选中座位信息显示
   - 实时价格计算
   - 快速移除已选座位

## 自定义配置

可以通过修改`SeatSelector.vue`中的`seatLayout`数据来自定义：

- 座位数量和布局
- 座位价格
- 初始座位状态

```javascript
seatLayout: [
  [
    { status: 'available', price: 35 },
    { status: 'occupied', price: 35 },
    // ... 更多座位
  ],
  // ... 更多行
]
```

## 样式自定义

组件使用CSS变量和类名，可以轻松自定义样式：

- `.seat.available`：可选座位样式
- `.seat.selected`：已选座位样式
- `.seat.occupied`：已占用座位样式
- `.screen`：屏幕样式

## 浏览器支持

- Chrome/Edge 88+
- Firefox 78+
- Safari 14+

## 技术栈

- Vue 3
- Vite
- CSS3 (Flexbox, Grid, Transform)

## 许可证

MIT License